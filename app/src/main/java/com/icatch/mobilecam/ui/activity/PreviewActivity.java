package com.icatch.mobilecam.ui.activity;

import android.animation.ObjectAnimator;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.GridLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.RadioButton;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.MyCamera.CameraManager;
import com.icatch.mobilecam.Presenter.PreviewPresenter;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.data.GlobalApp.GlobalInfo;
import com.icatch.mobilecam.data.Mode.PreviewMode;
import com.icatch.mobilecam.evenbus.BackgroundStitchingCountRefreshEvent;
import com.icatch.mobilecam.evenbus.EventBusUtil;
import com.icatch.mobilecam.evenbus.RemarkClearEvent;
import com.icatch.mobilecam.ui.ExtendComponent.MyProgressDialog;
import com.icatch.mobilecam.ui.ExtendComponent.ZoomView;
import com.icatch.mobilecam.ui.Interface.PreviewView;
import com.icatch.mobilecam.ui.adapter.SettingListAdapter;
import com.icatch.mobilecam.ui.appdialog.EditRemarkDialog;
import com.icatch.mobilecam.utils.CameraUtils;
import com.icatch.mobilecam.utils.ClickUtils;
import com.icatch.mobilecam.utils.ToastUtil;
import com.icatchtek.control.customer.type.ICatchCamEventID;
import com.icatchtek.control.customer.type.ICatchCamMode;
import com.ijoyer.camera.activity.MainActivity;
import com.ijoyer.mobilecam.R;
import com.kongzue.dialogx.dialogs.MessageDialog;
import com.kongzue.dialogx.util.TextInfo;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

public class PreviewActivity extends AppCompatActivity implements View.OnClickListener, PreviewView {
    private static final String TAG = PreviewActivity.class.getSimpleName();
    private PreviewPresenter previewPresenter;
    private SurfaceView mSurfaceView;
    private ImageButton pbBtn;
    private ImageButton captureBtn;
    private ImageView wbStatus;
    private ImageView burstStatus;
    private ImageView wifiStatus;
    private ImageView batteryStatus;
    private ImageView timeLapseMode;
    private ImageView slowMotion;
    private ImageView carMode;
    private TextView recordingTime;
    private ImageView autoDownloadImageView;
    private TextView delayCaptureText;
    private RelativeLayout delayCaptureLayout;
    private RelativeLayout imageSizeLayout;
    private RelativeLayout videoSizeLayout;
    private TextView remainRecordingTimeText;
    private TextView remainCaptureCountText;
    private TextView imageSizeTxv;
    private TextView videoSizeTxv;
    private ZoomView zoomView;
    private RelativeLayout setupMainMenu;
    private ListView mainMenuList;
    private ActionBar actionBar;
    private TextView noSupportPreviewTxv;
    private PopupWindow pvModePopupWindow;
    private RadioButton captureRadioBtn;
    private RadioButton videoRadioBtn;
    private RadioButton timeLapseRadioBtn;
    private ImageButton pvModeBtn;
    private View contentView;
    private ImageButton panoramaTypeBtn;
    private ImageButton pvSettingBtn;
    private String cameraName;
    private TextView tvRemark;
    private LinearLayout llDownloadCount;
    private TextView tvDownloadCount;
    private View vDownloadCount;
    private ObjectAnimator downloadCountAnimator;

    //A6Max 视频模式设置按钮
    private LinearLayout llA6MaxVideoMode;
    private View vA6MaxVideoLong, vA6MaxVideoShort;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (CameraManager.getInstance().getCurCamera() == null) {
            finish();
            return;
        }
        cameraName = CameraManager.getInstance().getCurCamera().getCameraFixedInfo().getCameraName();
        if ("A6S".equals(cameraName) && false) {
            requestWindowFeature(Window.FEATURE_NO_TITLE);
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
            this.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_REVERSE_LANDSCAPE);
            setTheme(R.style.NoTitle_FullScreen);
            setContentView(R.layout.activity_preview_reverse_landscape);
        } else {
            setContentView(R.layout.activity_preview);
            actionBar = getSupportActionBar();
            hideActionBar();
        }
        pvSettingBtn = (ImageButton) findViewById(R.id.pv_setting);
        previewPresenter = new PreviewPresenter(PreviewActivity.this);
        previewPresenter.setView(this);
        mSurfaceView = (SurfaceView) findViewById(R.id.preview);
        mSurfaceView.setOnClickListener(this);
        pbBtn = (ImageButton) findViewById(R.id.multi_pb);
        pbBtn.setOnClickListener(this);
        captureBtn = (ImageButton) findViewById(R.id.doCapture);
        captureBtn.setOnClickListener(this);
        wbStatus = (ImageView) findViewById(R.id.wb_status);
        burstStatus = (ImageView) findViewById(R.id.burst_status);
        wifiStatus = (ImageView) findViewById(R.id.wifi_status);
        batteryStatus = (ImageView) findViewById(R.id.battery_status);
        timeLapseMode = (ImageView) findViewById(R.id.timelapse_mode);
        slowMotion = (ImageView) findViewById(R.id.slow_motion);
        carMode = (ImageView) findViewById(R.id.car_mode);
        recordingTime = (TextView) findViewById(R.id.recording_time);
        autoDownloadImageView = (ImageView) findViewById(R.id.auto_download_imageView);
        delayCaptureText = (TextView) findViewById(R.id.delay_capture_text);
        delayCaptureLayout = (RelativeLayout) findViewById(R.id.delay_capture_layout);
        imageSizeLayout = (RelativeLayout) findViewById(R.id.image_size_layout);
        imageSizeTxv = (TextView) findViewById(R.id.image_size_txv);
        remainCaptureCountText = (TextView) findViewById(R.id.remain_capture_count_text);
        videoSizeLayout = (RelativeLayout) findViewById(R.id.video_size_layout);
        videoSizeTxv = (TextView) findViewById(R.id.video_size_txv);
        remainRecordingTimeText = (TextView) findViewById(R.id.remain_recording_time_text);
        setupMainMenu = (RelativeLayout) findViewById(R.id.setupMainMenu);
        mainMenuList = (ListView) findViewById(R.id.setup_menu_listView);
        noSupportPreviewTxv = (TextView) findViewById(R.id.not_support_preview_txv);
        pvModeBtn = (ImageButton) findViewById(R.id.pv_mode);
        contentView = LayoutInflater.from(PreviewActivity.this).inflate(R.layout.camera_mode_switch_layout, null);
        pvModePopupWindow = new PopupWindow(contentView, GridLayout.LayoutParams.WRAP_CONTENT, GridLayout.LayoutParams.WRAP_CONTENT, true);
        pvModePopupWindow.setBackgroundDrawable(new BitmapDrawable());
        pvModePopupWindow.setFocusable(true);
        pvModePopupWindow.setOutsideTouchable(true);
        captureRadioBtn = (RadioButton) contentView.findViewById(R.id.capture_radio);
        videoRadioBtn = (RadioButton) contentView.findViewById(R.id.video_radio);
        timeLapseRadioBtn = (RadioButton) contentView.findViewById(R.id.timeLapse_radio);
        llDownloadCount = (LinearLayout) findViewById(R.id.llDownloadCount);
        tvDownloadCount = (TextView) findViewById(R.id.tvDownloadCount);
        vDownloadCount = findViewById(R.id.vDownloadCount);

        zoomView = (ZoomView) findViewById(R.id.zoom_view);
        zoomView.setZoomInOnclickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!ClickUtils.isFastDoubleClick(v)) {
                    previewPresenter.zoomIn();
                }
            }
        });
        zoomView.setZoomOutOnclickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!ClickUtils.isFastDoubleClick(v)) {
                    previewPresenter.zoomOut();
                }
            }
        });
        zoomView.setOnSeekBarChangeListener(new ZoomView.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(ZoomView zoomView, float progress, boolean fromUser) {
            }

            @Override
            public void onStartTrackingTouch(ZoomView zoomView) {
            }

            @Override
            public void onStopTrackingTouch(ZoomView zoomView) {
                previewPresenter.zoomBySeekBar();
            }
        });
        mainMenuList.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                if (!ClickUtils.isFastDoubleClick(mainMenuList)) {
                    previewPresenter.showSettingDialog(position);
                }
            }
        });
        pvSettingBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (!ClickUtils.isFastDoubleClick(pvSettingBtn)) {
                    if (View.GONE == setupMainMenu.getVisibility()) {
                        previewPresenter.loadSettingMenuList();
                    } else {
                        setupMainMenu.setVisibility(View.GONE);
                    }
                }
            }
        });
        pvModeBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!ClickUtils.isFastDoubleClick(v)) {
                    String checkCanChangeModeErr =  previewPresenter.canChangeMode();
                    if (TextUtils.isEmpty(checkCanChangeModeErr)){
                        previewPresenter.showPvModePopupWindow();
                    }else{
                        ToastUtil.showShortToast(checkCanChangeModeErr);
                    }
                }
            }
        });
        captureRadioBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!ClickUtils.isFastDoubleClick(v)) {
                    setupMainMenu.setVisibility(View.GONE);
                    previewPresenter.changePreviewMode(PreviewMode.APP_STATE_STILL_MODE);
                }
            }
        });
        videoRadioBtn.setOnClickListener(v -> {
            if (!ClickUtils.isFastDoubleClick(v)) {
                setupMainMenu.setVisibility(View.GONE);
                previewPresenter.changePreviewMode(PreviewMode.APP_STATE_VIDEO_MODE);
            }
        });
        timeLapseRadioBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!ClickUtils.isFastDoubleClick(v)) {
                    setupMainMenu.setVisibility(View.GONE);
                    previewPresenter.changePreviewMode(PreviewMode.APP_STATE_TIMELAPSE_MODE);
                }
            }
        });

        mSurfaceView.getHolder().addCallback(new SurfaceHolder.Callback() {
            @Override
            public void surfaceCreated(SurfaceHolder holder) {
                AppLog.d(TAG, "surfaceCreated!!!");
                previewPresenter.initSurface(mSurfaceView.getHolder());
                previewPresenter.startPreview();
//                mSurfaceView.getHolder().lockCanvas().rotate(90);
            }

            @Override
            public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {
                AppLog.d(TAG, "surfaceChanged!!!");
                previewPresenter.setDrawingArea(width, height);
            }

            @Override
            public void surfaceDestroyed(SurfaceHolder holder) {
                previewPresenter.destroyPreview();
            }
        });
        mSurfaceView.setOnTouchListener((v, event) -> {
            switch (event.getAction() & MotionEvent.ACTION_MASK) {
                case MotionEvent.ACTION_DOWN:
                    previewPresenter.onSufaceViewTouchDown(event);
                    break;
                case MotionEvent.ACTION_POINTER_DOWN:
                    previewPresenter.onSufaceViewPointerDown(event);
                    break;
                case MotionEvent.ACTION_MOVE:
                    previewPresenter.onSufaceViewTouchMove(event);
                    break;
                case MotionEvent.ACTION_UP:
                    previewPresenter.onSufaceViewTouchUp();
                    break;
                case MotionEvent.ACTION_POINTER_UP:
                    previewPresenter.onSufaceViewTouchPointerUp();
                    break;
            }
            return true;
        });
        GlobalInfo.getInstance().addEventListener(ICatchCamEventID.ICH_CAM_EVENT_SDCARD_REMOVED);
        GlobalInfo.getInstance().addEventListener(ICatchCamEventID.ICH_CAM_EVENT_SDCARD_IN);
        GlobalInfo.getInstance().addEventListener(ICatchCamEventID.ICH_CAM_EVENT_CONNECTION_DISCONNECTED);
        panoramaTypeBtn = (ImageButton) findViewById(R.id.panorama_type_btn);
        panoramaTypeBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (!ClickUtils.isFastDoubleClick(panoramaTypeBtn)) {
                    previewPresenter.setPanoramaType();
                }
            }
        });

        tvRemark = findViewById(R.id.tvRemark);
        View vRemark = findViewById(R.id.llRemark);
        if (CameraUtils.isA6SOrA8(cameraName)) {
            vRemark.setVisibility(View.VISIBLE);
            vRemark.setOnClickListener(this::showEditRemarkDialog);
        } else {
            vRemark.setVisibility(View.GONE);
        }

        llA6MaxVideoMode = findViewById(R.id.llA6MaxVideoMode);
        vA6MaxVideoLong = findViewById(R.id.vA6MaxVideoLong);
        vA6MaxVideoShort = findViewById(R.id.vA6MaxVideoShort);
        findViewById(R.id.llA6MaxVideoModeLong).setOnClickListener(v -> {
            previewPresenter.setA6MaxVideoMode(true);
        });
        findViewById(R.id.llA6MaxVideoModeShort).setOnClickListener(v -> {
            previewPresenter.setA6MaxVideoMode(false);
        });
        findViewById(R.id.ib_back).setOnClickListener(v ->
                back());


        EventBusUtil.register(this);
    }

    public void hideActionBar() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            View decorView = getWindow().getDecorView();
            int option = View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_LAYOUT_STABLE;
            decorView.setSystemUiVisibility(option);
            getWindow().setNavigationBarColor(Color.TRANSPARENT);
            getWindow().setStatusBarColor(Color.TRANSPARENT);
        }
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.hide();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        AppLog.d(TAG, "onResume");
        previewPresenter.submitAppInfo();
        previewPresenter.initPreview();
        previewPresenter.initStatus();
        previewPresenter.addEvent();
        AppInfo.checkLocationDialog(this);
    }

    @Override
    protected void onStop() {
        AppLog.d(TAG, "onStop");
        super.onStop();
        previewPresenter.isAppBackground();
    }

    @Override
    public void finish() {
        MainActivity.lastExitPreviewActivityTime = System.currentTimeMillis();//设置退出时间
        super.finish();
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        switch (keyCode) {
            case KeyEvent.KEYCODE_HOME:
                AppLog.d("AppStart", "home");
                break;
            case KeyEvent.KEYCODE_BACK:
                AppLog.d("AppStart", "back");
                back();
                break;
            default:
                return super.onKeyDown(keyCode, event);
        }
        return true;
    }

    @Override
    protected void onDestroy() {
        MyProgressDialog.closeProgressDialog();
        EventBusUtil.unRegister(this);
        AppLog.d(TAG, "onDestroy");
        LogUtils.file("PreviewActivity:正常销毁");
        if (previewPresenter != null) {
            previewPresenter.onDestroy();
            previewPresenter.removeActivity();
            previewPresenter.destroyPreview();
            previewPresenter.delEvent();
            previewPresenter.disconnectCamera();
            previewPresenter.delConnectFailureListener();
            previewPresenter.unregisterWifiSSReceiver();
        }
        super.onDestroy();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_preview, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (id == android.R.id.home) {
            AppLog.e(TAG, "id == android.R.id.home");
            previewPresenter.finishActivity();
        } else if (id == R.id.action_setting) {
            if (!ClickUtils.isFastDoubleClick(id)) {
                previewPresenter.loadSettingMenuList();
            }
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onClick(View v) {
        AppLog.i(TAG, "click the v.getId() =" + v.getId());
        int id = v.getId();
        if (id == R.id.multi_pb) {
            AppLog.i(TAG, "click the multi_pb");
            if (!ClickUtils.isFastDoubleClick(R.id.multi_pb)) {
//                previewPresenter.redirectToAnotherActivity(PreviewActivity.this, RemoteMultiPbActivity.class);
                //直接进入手机相册
                previewPresenter.redirectToAnotherActivity(PreviewActivity.this, LocalMultiPbActivity.class);
            }
        } else if (id == R.id.doCapture) {
            AppLog.i(TAG, "click the doCapture");
            if (!ClickUtils.isFastDoubleClick(R.id.doCapture)) {
                previewPresenter.startOrStopCapture();
            }
        }
    }

    private void showEditRemarkDialog(View v) {
        EditRemarkDialog.show(tvRemark.getText().toString(), remark -> {
            previewPresenter.currentRemarkName = remark;
            tvRemark.setText(remark);
        });
    }

    @Override
    public void setWbStatusVisibility(int visibility) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                wbStatus.setVisibility(visibility);
            }
        });
    }

    @Override
    public void setBurstStatusVisibility(int visibility) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                burstStatus.setVisibility(visibility);
            }
        });
    }

    @Override
    public void setWifiStatusVisibility(int visibility) {
        ThreadUtils.runOnUiThread(() -> wifiStatus.setVisibility(visibility));
    }

    @Override
    public void setWifiIcon(int drawableId) {
        ThreadUtils.runOnUiThread(() -> wifiStatus.setBackgroundResource(drawableId));
    }

    @Override
    public void setBatteryStatusVisibility(int visibility) {
        ThreadUtils.runOnUiThread(() -> batteryStatus.setVisibility(View.INVISIBLE));
    }

    @Override
    public void setBatteryIcon(int drawableId) {
        ThreadUtils.runOnUiThread(() -> {
            batteryStatus.setVisibility(View.VISIBLE);
            batteryStatus.setBackgroundResource(drawableId);
        });
    }

    @Override
    public void setTimeLapseModeVisibility(int visibility) {
        ThreadUtils.runOnUiThread(() -> timeLapseMode.setVisibility(visibility));
    }

    @Override
    public void setTimeLapseModeIcon(int drawableId) {
        ThreadUtils.runOnUiThread(() -> timeLapseMode.setBackgroundResource(drawableId));
    }

    @Override
    public void setSlowMotionVisibility(int visibility) {
        ThreadUtils.runOnUiThread(() -> slowMotion.setVisibility(visibility));
    }

    @Override
    public void setCarModeVisibility(int visibility) {
        ThreadUtils.runOnUiThread(() -> carMode.setVisibility(visibility));
    }

    @Override
    public void setRecordingTimeVisibility(int visibility) {
        ThreadUtils.runOnUiThread(() -> recordingTime.setVisibility(visibility));
    }

    @Override
    public void setAutoDownloadVisibility(int visibility) {
        ThreadUtils.runOnUiThread(() -> autoDownloadImageView.setVisibility(visibility));
    }

    @Override
    public void setCaptureBtnBackgroundResource(int id) {
        ThreadUtils.runOnUiThread(() -> captureBtn.setBackgroundResource(id));
    }

    @Override
    public void setRecordingTime(String lapseTime) {
        ThreadUtils.runOnUiThread(() -> recordingTime.setText(lapseTime));
    }

    @Override
    public void setDelayCaptureLayoutVisibility(int visibility) {
        ThreadUtils.runOnUiThread(() -> delayCaptureLayout.setVisibility(visibility));
    }

    @Override
    public void setDelayCaptureTextTime(String delayCaptureTime) {
        ThreadUtils.runOnUiThread(() -> delayCaptureText.setText(delayCaptureTime));
    }

    @Override
    public void setImageSizeLayoutVisibility(int visibility) {
        ThreadUtils.runOnUiThread(() -> imageSizeLayout.setVisibility(visibility));
    }

    @Override
    public void setRemainCaptureCount(String remainCaptureCount) {
        ThreadUtils.runOnUiThread(() -> remainCaptureCountText.setText(remainCaptureCount));
    }

    @Override
    public void setVideoSizeLayoutVisibility(int visibility) {
        ThreadUtils.runOnUiThread(() -> {
            videoSizeLayout.setVisibility(visibility);
            remainRecordingTimeText.setVisibility(visibility);
        });

    }

    @Override
    public void setRemainRecordingTimeText(String remainRecordingTime) {
        ThreadUtils.runOnUiThread(() -> remainRecordingTimeText.setText(remainRecordingTime));
    }

    @Override
    public void setBurstStatusIcon(int drawableId) {
        burstStatus.setBackgroundResource(drawableId);
    }

    @Override
    public void setWbStatusIcon(int drawableId) {
        wbStatus.setBackgroundResource(drawableId);
    }

    @Override
    public void setUpsideVisibility(int visibility) {
        carMode.setVisibility(visibility);
    }

    @Override
    public void setCaptureBtnEnAbility(boolean enAbility) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                captureBtn.setEnabled(enAbility);
            }
        });
    }

    @Override
    public void setVideoSizeInfo(String sizeInfo) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                videoSizeTxv.setText(sizeInfo);
            }
        });
    }

    @Override
    public void setImageSizeInfo(String sizeInfo) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                imageSizeTxv.setText(sizeInfo);
            }
        });
    }

    @Override
    public void showZoomView() {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                zoomView.startDisplay();
            }
        });
    }

    @Override
    public void hideZoomView() {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                zoomView.setHide();
            }
        });
    }

    @Override
    public void setMaxZoomRate(final float maxZoomRate) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                zoomView.setMaxValue(maxZoomRate);
            }
        });
    }

    @Override
    public void setMinZoomRate(final float minZoomRate) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                zoomView.setMinValue(minZoomRate);
            }
        });
    }

    @Override
    public float getZoomViewProgress() {
        return zoomView.getProgress();
    }

    @Override
    public float getZoomViewMaxZoomRate() {
        return ZoomView.MAX_VALUE;
    }

    @Override
    public void updateZoomViewProgress(float currentZoomRatio) {
        zoomView.updateZoomBarValue(currentZoomRatio);
        zoomView.setProgress(currentZoomRatio);
    }

    @Override
    public int getSetupMainMenuVisibility() {
        return setupMainMenu.getVisibility();
    }

    @Override
    public void setSetupMainMenuVisibility(int visibility) {
        setupMainMenu.setVisibility(visibility);
    }

    @Override
    public void setAutoDownloadBitmap(Bitmap bitmap) {
        if (bitmap != null) {
            autoDownloadImageView.setImageBitmap(bitmap);
        }
    }

    @Override
    public void setActionBarTitle(int resId) {
        actionBar.setTitle(resId);
    }

    @Override
    public void setBackBtnVisibility(boolean isVisible) {
        actionBar.setDisplayHomeAsUpEnabled(isVisible);
    }

    @Override
    public void setSettingMenuListAdapter(SettingListAdapter settingListAdapter) {
        mainMenuList.setAdapter(settingListAdapter);
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                previewPresenter.redrawSurface();
            }
        }, 200);
        AppLog.d(TAG, "onConfigurationChanged newConfig Orientation=" + newConfig.orientation);
    }

    @Override
    public void setSupportPreviewTxvVisibility(int visibility) {
        noSupportPreviewTxv.setVisibility(visibility);
    }

    @Override
    public void setPvModeBtnBackgroundResource(int drawableId) {
        pvModeBtn.setBackgroundResource(drawableId);
    }

    @Override
    public void setPvModeBtnVisibility(int visibility) {
        pvModeBtn.setVisibility(visibility);
    }

    @Override
    public void setTimeLapseRadioBtnVisibility(int visibility) {
        timeLapseRadioBtn.setVisibility(visibility);
    }

    @Override
    public void setCaptureRadioBtnVisibility(int visibility) {
        captureRadioBtn.setVisibility(visibility);
    }

    @Override
    public void setVideoRadioBtnVisibility(int visibility) {
        videoRadioBtn.setVisibility(visibility);
    }

    @Override
    public void setTimeLapseRadioChecked(boolean checked) {
        timeLapseRadioBtn.setChecked(checked);
    }

    @Override
    public void setCaptureRadioBtnChecked(boolean checked) {
        captureRadioBtn.setChecked(checked);
    }

    @Override
    public void setVideoRadioBtnChecked(boolean checked) {
        videoRadioBtn.setChecked(checked);
    }

    @Override
    public void showPopupWindow(int curMode) {
        if (pvModePopupWindow != null) {
            int dp_10 = getResources().getDimensionPixelOffset(R.dimen.dp_10);
            int xOff = -dp_10, yOff;
            //A6Max 没有延时摄影，做额外的处理
            if (CameraUtils.isA6Max(cameraName) || !CameraManager.getInstance().getCurCamera()
                    .getCameraProperties().cameraModeSupport(ICatchCamMode.ICH_CAM_MODE_VIDEO)) {
                yOff = -findViewById(R.id.bottomBar).getHeight() * 2 - dp_10;
            } else {
                yOff = -findViewById(R.id.bottomBar).getHeight() * 3 + pvModeBtn.getHeight() - dp_10;
            }
            pvModePopupWindow.showAsDropDown(pvModeBtn, xOff, yOff);
        }
    }

    @Override
    public void dismissPopupWindow() {
        if (pvModePopupWindow != null) {
            if (pvModePopupWindow.isShowing()) {
                pvModePopupWindow.dismiss();
            }
        }
    }

    @Override
    public int getSurfaceViewWidth() {
        View parentView = (View) mSurfaceView.getParent();
        int width = parentView.getWidth();
        return width;
    }

    @Override
    public int getSurfaceViewHeight() {
        View parentView = (View) mSurfaceView.getParent();
        int heigth = parentView.getHeight();
        return heigth;
    }

    @Override
    public void setPanoramaTypeBtnSrc(int srcId) {
        panoramaTypeBtn.setImageResource(srcId);
    }

    @Override
    public void setPanoramaTypeBtnVisibility(int visibility) {
        panoramaTypeBtn.setVisibility(visibility);
    }

    @Override
    public void setA6MaxRecordMode(Boolean isA6MaxLongRecordMode) {
        if (isA6MaxLongRecordMode == null) {
            llA6MaxVideoMode.setVisibility(View.GONE);
        } else {
            llA6MaxVideoMode.setVisibility(View.VISIBLE);
            vA6MaxVideoLong.setBackgroundResource(isA6MaxLongRecordMode ?
                    R.drawable.ic_check_yes : R.drawable.ic_check_no);
            vA6MaxVideoShort.setBackgroundResource(!isA6MaxLongRecordMode ?
                    R.drawable.ic_check_yes : R.drawable.ic_check_no);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onRemarkClearEvent(RemarkClearEvent event) {
        tvRemark.setText("");
    }

    //拼接中的数量刷新通知
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onBackgroundStitchFinishEvent(BackgroundStitchingCountRefreshEvent event) {
        if (isFinishing() || !CameraUtils.isA6SOrA8(cameraName) || llDownloadCount == null) {
            return;
        }
        if (event.count > 0) {
            llDownloadCount.setVisibility(View.VISIBLE);
            tvDownloadCount.setText(String.valueOf(event.count));
            //对vDownloadCount重复做向下移动的动画，模拟下载效果
            if (downloadCountAnimator != null) {
                downloadCountAnimator.cancel();
            }
            vDownloadCount.post(() -> {
                downloadCountAnimator = ObjectAnimator.ofFloat(vDownloadCount,
                        "translationY", -vDownloadCount.getHeight(), vDownloadCount.getHeight());
                downloadCountAnimator.setDuration(1000); // 动画持续时间
                downloadCountAnimator.setRepeatCount(ObjectAnimator.INFINITE); // 无限重复
                downloadCountAnimator.setRepeatMode(ObjectAnimator.RESTART); //
                downloadCountAnimator.start();
            });

        } else {
            llDownloadCount.setVisibility(View.GONE);
            downloadCountAnimator.cancel();
        }
    }

    /**
     * 处理返回键，显示二次确认对话框
     */
    public void back() {
        MessageDialog dialog = MessageDialog.build()
                .setTitle("是否退出拍摄")
                .setCancelButton("取消")
                .setOkButton("确认")
                .setOkButton((baseDialog, v) -> {
                    // 用户确认退出，调用原来的退出逻辑
                    previewPresenter.finishActivity();
                    return false;
                })
                .setCancelButton((baseDialog, v) -> {
                    // 用户取消，不做任何操作
                    return false;
                });
        dialog.setTitleTextInfo(new TextInfo().setFontSize(16));
        dialog.show();
    }
}
